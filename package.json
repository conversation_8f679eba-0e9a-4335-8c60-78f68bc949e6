{"name": "tongchuang_vue", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"core-js": "^3.8.3", "echarts": "^5.6.0", "element-ui": "^2.15.14", "postcss-plugin-px2rem": "^0.8.1", "vue": "^2.6.14", "vue-count-to": "^1.0.13", "vue-router": "^3.2.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "css-loader": "^7.1.2", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.89.2", "sass-loader": "^10.5.2", "style-loader": "^4.0.0", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}