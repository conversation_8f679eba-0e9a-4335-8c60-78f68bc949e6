module.exports = {
	plugins: [
		require('postcss-plugin-px2rem')({
			rootValue: 192, // 设计稿宽度 / 10，如设计稿宽度为 750px，则 rootValue 为 75
			unitPrecision: 5, // 转换后的 rem 值保留的小数位数
			propWhiteList: [], // 允许转换的属性，空数组表示所有属性
			propBlackList: [], // 禁止转换的属性
			exclude: /(node_module)/, // 排除 node_modules 目录
			selectorBlackList: [], // 禁止转换的选择器
			ignoreIdentifier: false, // 是否忽略特定标识符
			replace: true, // 是否直接替换 px 值
			mediaQuery: false, // 是否转换媒体查询中的 px
			minPixelValue: 0 // 小于等于该值的 px 不转换
		})
	]
}