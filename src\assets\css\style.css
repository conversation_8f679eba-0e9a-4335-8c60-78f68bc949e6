@font-face {
	font-family: DIN;
	src: url('DIN-Medium.otf')
}

@font-face {
	font-family: FontquanXinYiGuanHeiTi;
	src: url('FontquanXinYiGuanHeiTi.ttf')
}




body,
input,
h1,
h2,
h3,
h4,
h5,
h6,
p,
textarea,
table,
div,
ul,
li,
select,
td,
th,
em,
span,
i,
button,
code {
	margin: 0;
	padding: 0;
	color: inherit;
	line-height: inherit;
	font-family: 'PingFang SC', 'PingFang SC', "microsoft yahei", sans-serif;
	font-size: 14px;

}

img {
	border: 0;
	display: inline-block;
	max-width: 100%;
	vertical-align: middle;
}

.fl {
	float: left;
}

.fr {
	float: right;
}

ul,
li {
	list-style: none;
}

a,
p,
span,
i,
em {
	text-decoration: none;
	color: inherit;
}

:focus {
	outline: none !important;
	border-color: inherit;
}

.clearfix {
	*zoom: 1;
}

.clearfix:before,
.clearfix:after {
	display: table;
	line-height: 0;
	content: "";
}

.clearfix:after {
	clear: both;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
	width: 100%
}

h3,
h4,
h5,
h6 {
	font-weight: normal;
}

em,
i,
a,
p,
span {
	font-style: normal;
	font-size: inherit;
	color: inherit;
}

* {
	box-sizing: border-box;
	/* color: #fff; */
}

input[type="button"],
input[type="submit"],
input[type="reset"],
input[type="text"] {
	-webkit-appearance: none;
}

input[type="button"] {
	border: none;
}

textarea {
	-webkit-appearance: none;
}

:hover {
	transition: all .2s;
}

.img_hover_wrap {
	overflow: hidden;
}

.img_hover {
	transition: all .3s;
}

.img_hover:hover {
	transform: scale(1.05, 1.05);
}

.img_traslate {
	transition: all .3s;
}

.img_traslate:hover {
	transform: translateY(-10px);
}

.img_rotate {
	transition: all .3s;
}

.img_rotate:hover {
	transform: rotate(-360deg);
}

.bold {
	font-weight: bold;
}

.normal {
	font-weight: normal !important;
}

.center {
	text-align: center !important;
}

.img {
	/* width: 100%; */
	display: block;
}

.ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.ele {
	position: relative;
}

.ele:before,
.ele:after {
	content: '';
	display: inline-block;
	vertical-align: middle;
}

.ipt {
	display: block;
	border: none;
}

.ipt_button {
	cursor: pointer;
}

.item_scale .icon {
	transition: all .3s;
}

.item_scale:hover .icon {
	transform: scale(1.05, 1.05);
}

.text-left {
	text-align: left !important;
}

.text-right {
	text-align: right !important;
}

.clamp {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box !important;
	-webkit-box-orient: vertical;
	/*-webkit-line-clamp: 3;*/
}


body {
	overflow-x: hidden;
}

a {
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.item {}

.item .imgbox {
	display: block;
	overflow: hidden;
}

.item .img {
	transition: all 1s;
	width: 100%;
	display: block;
}

.item:hover .imgbox .img {
	transform: scale(1.1, 1.1);
}

.zoomimg {
	position: relative;
	overflow: hidden;
	height: 0;
	display: block;
}

.zoomimg .img {
	display: block;
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
}

.relative {
	position: relative;
}

.flipx .icon {
	transition: all .3s;
}

.flipx:hover .icon {
	transform: scaleX(-1);
}

.scale .icon {
	transition: all .3s;
}

.scale:hover .icon {
	transform: scale(1.1, 1.1);
}

.underline:hover {
	text-decoration: underline;
}

label {
	-webkit-tap-highlight-color: rgba(255, 0, 0, 0);
}

.flex {
	display: -webkit-box;
	display: -moz-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;
}

.flex-align-start {
	align-items: flex-start;
}

.flex-align-center {
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
}

.flex-align-end {
	align-items: flex-end;
}

.flex-pack-center {
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
}

.flex-pack-justify {
	-webkit-box-pack: justify;
	-webkit-justify-content: pace-between;
	-ms-flex-pack: justify;
	justify-content: space-between;
}

.flex-pack-start {
	justify-content: flex-start;
}

.flex-pack-end {
	justify-content: flex-end;
}

.flex-v {
	-webkit-box-orient: vertical;
	-webkit-flex-direction: column;
	-ms-flex-direction: column;
	flex-direction: column;
	display: flex;
	flex-direction: column;
}

.flex-w {
	flex-wrap: wrap;
}

.flex-1 {
	flex: 1;
	overflow: hidden;
	/* overflow: auto;
	overflow-x: hidden;
	min-height: 0; */

}

.flex-11 {
	flex: 1;
	overflow: auto;
	overflow-x: hidden;
	min-height: 0;

}

.none {
	display: none;
}

.block {
	display: block;
}

.scrolly {
	overflow-y: scroll;
}

.scrolly::-webkit-scrollbar {
	width: 0;
}

.scrollybg {
	overflow-y: auto !important;
	padding-right: 10px;
	width: calc(100% + 15px);
}

.scrollybg::-webkit-scrollbar {
	width: 5px;
}

::-webkit-scrollbar {
	width: 5px;
	height: 6px;
}

::-webkit-scrollbar-track {
	background-color: transparent;
}

::-webkit-scrollbar-thumb {
	background-color: rgba(255,255,255,.2);
	border-radius: 3px;
}

::-webkit-scrollbar-button {
	display: none;
}

::-webkit-scrollbar-resizer {
	display: none;
}

.img_full {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

::placeholder {
	color: #fff !important;
}

::-webkit-input-placeholder {
	color: #fff !important;
}

:-moz-placeholder {
	color: #fff !important;
}

:-ms-input-placeholder {
	color: #fff !important;
}




/* 公共部分 */
