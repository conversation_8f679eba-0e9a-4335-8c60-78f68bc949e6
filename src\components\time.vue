<template>
	<div class="flex flex-align-center">
		<span class="weather">
			<img src="../assets/images/icon_04.png" class="ic" alt="">
			晴
		</span>
		<div class="time">
			{{formattedDate}}{{formattedTime}}
		</div>
	</div>
</template>


<script>
	export default {
		data() {
			return {
				currentDate: new Date(),
			}
		},
		computed: {
			// 计算属性：格式化日期（年-月-日）
			formattedDate() {
				const year = this.currentDate.getFullYear();
				const month = String(this.currentDate.getMonth() + 1).padStart(2, '0');
				const day = String(this.currentDate.getDate()).padStart(2, '0');
				return `${year}年${month}月${day}日`;
			},
			// 计算属性：格式化时间（时:分:秒）
			formattedTime() {
				const hours = String(this.currentDate.getHours()).padStart(2, '0');
				const minutes = String(this.currentDate.getMinutes()).padStart(2, '0');
				const seconds = String(this.currentDate.getSeconds()).padStart(2, '0');
				return `${hours}:${minutes}:${seconds}`;
			},
		},
		mounted() {
			// 组件挂载后启动定时器，每秒更新一次时间
			this.timer = setInterval(() => {
				this.currentDate = new Date();
			}, 1000);
		},
		beforeDestroy() {
			// 组件销毁前清除定时器，防止内存泄漏
			if (this.timer) {
				clearInterval(this.timer);
			}
		},
	}
</script>


<style lang="scss" scoped>
	.weather {
		display: flex;
		align-items: center;
		font-size: 14px;
		color: #fff;
		margin-right: 15px;

		.ic {
			margin-right: 5px;
			width: 48px;
		}
	}

	.time {
		font-size: 14px;
		color: #fff;
		letter-spacing: 1px;
	}
</style>