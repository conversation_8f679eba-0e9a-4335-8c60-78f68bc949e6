import Vue from 'vue'
import App from './App.vue'
import router from './router'
// import 'swiper/dist/css/swiper.min.css'
// import 'swiper/dist/js/swiper.min.js'
// import 'assets/iconfont/iconfont.css'
import 'assets/css/style.css'

import 'assets/js/flexible.js'
import 'assets/js/echarts-resize.js'




// element
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';

// echarts
import echarts from "echarts";
Vue.prototype.$echarts = echarts;


Vue.config.productionTip = false
Vue.use(router);
Vue.use(ElementUI);
new Vue({
	router,
	render: h => h(App),
}).$mount('#app')