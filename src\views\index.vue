<template>
	<div class="body flex-v">
		<div class="head">
			<div class="l">
				<HeadTime />
			</div>
			<img src="../assets/images/logo.png" class="logo" alt="" />
			<div class="r">
				<div class="fullscreen" @click="toggleFullscreen">
					<span v-if="isFullscreen">
						<img src="../assets/images/idx_03.png" class="ico" alt="" />退出
					</span>
					<span v-else>
						<img src="../assets/images/idx_03.png" class="ico" alt="" />全屏
					</span>
				</div>
			</div>
		</div>

		<div class="flex-1 container flex">
			<div class="con-l flex-v">
				<div class="m mod1">
					<ul class="list">
						<li class="item">
							<p class="fz">今日GMV</p>
							<p class="number text">
								<count-to :start-val="0" :end-val="19536" :duration="2000"></count-to>
							</p>
						</li>
						<li class="item">
							<p class="fz">本月GMV</p>
							<p class="number text">
								<count-to :start-val="0" :end-val="2607282" :duration="2000"></count-to>
							</p>
						</li>
					</ul>
				</div>
				<div class="m mod2">
					<div class="detail">
						<div class="idx-tt">
							<h4 class="bt"><span>本月GMV排名</span></h4>
						</div>
						<div class="chart" ref="chart1" style="height:29vh;"></div>
					</div>
					<img src="../assets/images/idx_07.png" class="shuiyin" alt="" />
				</div>
				<div class="m mod3 flex-1">
					<div class="detail flex-v">
						<div class="idx-tt">
							<h4 class="bt"><span>年度任务GMV完成排名</span></h4>
						</div>
						<div class="flex-1 scrollybg">
							<div class="table">
								<div class="thead">
									<div class="th" style="width:35%;">主播</div>
									<div class="th" style="width:30%;">年度目标</div>
									<div class="th" style="width:35%;">当前完成</div>
								</div>
								<div class="tbody">
									<!-- <div class="tr">
										<div class="td flex flex-align-center" style="width:35%;">
											<div class="imgbox">
												<img src="../assets/images/idx_13.png" class="hdpic" alt="" />
											</div>
											王云
										</div>
										<div class="td" style="width:30%;">
											1200万
										</div>
										<div class="td flex flex-align-center" style="width:35%;">
											<div class="smb">
												<p style="color:#9698cd;">完成时间</p>
												<p>2025/06/03</p>
											</div>
											<span class="percent">100%</span>
										</div>
									</div> -->
									<div class="tr" v-for="item in taskRankList">
										<div class="td flex flex-align-center" style="width:35%;">
											<div class="imgbox">
												<img :src="item.img" class="hdpic" alt="" />
											</div>
											{{item.name}}
										</div>
										<div class="td" style="width:30%;">
											1200万
										</div>
										<div class="td flex flex-align-center" style="width:35%;">

											<div class="smb" v-if="item.percent=='100%'">
												<p style="color:#9698cd;">完成时间</p>
												<p>2025/06/03</p>
											</div>

											<div class="smb progress" v-else>
												<el-progress :percentage="40" :stroke-width="12"
													:show-text="false"></el-progress>
											</div>

											<span class="percent">{{item.percent}}</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<img src="../assets/images/idx_07.png" class="shuiyin" alt="" />
				</div>
			</div>
			<div class="flex-1 con-c flex-v">
				<div class="mod4">
					<div class="top">
						<div class="title">2025年度GMV</div>
						<div class="number total">
							25,690,012
						</div>
						<div class="bar">
							<span class="wz">目标值</span>
							<div class="progress">
								<el-progress :percentage="50" :stroke-width="12" :show-text="false"></el-progress>
							</div>
							<span class="wz number" style="color:#fff;">100,000,000</span>
						</div>
					</div>
					<ul class="list">
						<li class="item" v-for="item in 3">
							<img src="../assets/images/idx_13.png" class="hdpic" alt="" />
							<div class="info">
								<h6 class="name">王云</h6>
								<div class="number num">
									<count-to :start-val="0" :end-val="19536" :duration="3000"></count-to>
								</div>
								<div class="nmz">目标值：<span class="number">999999</span></div>
							</div>
						</li>
					</ul>
				</div>
				<div class="m mod5 flex-1 flex-v">
					<div class="titbox">
						<h5 class="bt">主播概览</h5>
					</div>
					<div class="flex-1">
						<el-table :data="tableData" height="100%">
							<el-table-column label="主播" sortable>
								<template slot-scope="scope">
									<div class="flex flex-align-center">
										<div class="imgbox">
											<img :src="scope.row.kuaizhao" class="pic" alt="" />
										</div>
										<span class="name">王云</span>
									</div>
								</template>
							</el-table-column>
							<el-table-column prop="leixing" label="主播类型" align="center" sortable>
							</el-table-column>
							<el-table-column prop="zuigao" label="历史最高GMV（月度）" width="200" align="center" sortable>
							</el-table-column>
							<el-table-column prop="niandu" label="GMV年度目标" align="center" sortable>
							</el-table-column>
							<el-table-column prop="yiwan" label="已完成GMV" align="center" sortable>
								<template slot-scope="scope">
									<span style="color:#f554e7;">{{scope.row.yiwan}}</span>
								</template>
							</el-table-column>
							<el-table-column prop="daiwan" label="待完成GMV" align="center" sortable>
							</el-table-column>
						</el-table>
					</div>
				</div>
			</div>
			<div class="con-r flex-v">
				<div class="m mod6">
					<div class="detail">
						<div class="idx-tt">
							<h4 class="bt"><span>今日人气榜</span></h4>
						</div>
						<div class="chart" ref="chart2" style="height:29vh;"></div>
					</div>
					<img src="../assets/images/idx_07.png" class="shuiyin" alt="" />
				</div>
				<div class="m mod7 flex-1">
					<div class="detail flex-v">
						<div class="idx-tt">
							<h4 class="bt"><span>当前GMV(</span></h4>
						</div>
						<div class="number total">
							<count-to :start-val="0" :end-val="2607282" :duration="2000"></count-to>
						</div>
						<ul class="list flex-1 scrollybg">
							<li class="item" v-for="item in 7">
								<div class="lbox">
									<div class="imgbox">
										<img src="../assets/images/idx_13.png" class="hdpic" alt="" />
										<span class="zt">卖货中</span>
									</div>
									<span class="number name">王云</span>
								</div>
								<div class="rbox">
									<div class="wz">20号直播间</div>
									<div class="number num">50,536</div>
								</div>
							</li>
						</ul>
					</div>
					<img src="../assets/images/idx_19.png" class="shuiyin" alt="" />
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import HeadTime from '@/components/time.vue'
	import countTo from 'vue-count-to'
	let Echarts = require('echarts/lib/echarts')
	export default {
		data() {
			return {
				chart1: null,
				chart2: null,

				taskRankList: [{
					img: require('../assets/images/idx_13.png'),
					name: '王云',
					percent: '100%',
				}, {
					img: require('../assets/images/idx_13.png'),
					name: '代秦雪',
					percent: '40%',
				}, {
					img: require('../assets/images/idx_13.png'),
					name: '王云',
					percent: '30%',
				}, {
					img: require('../assets/images/idx_13.png'),
					name: '代秦雪',
					percent: '20%',
				}, {
					img: require('../assets/images/idx_13.png'),
					name: '代秦雪',
					percent: '20%',
				}],

				tableData: [{
					kuaizhao: require('../assets/images/idx_13.png'),
					leixing: 'S主播导师',
					zuigao: '127万',
					niandu: '1200万',
					yiwan: '350万',
					daiwan: '550万'
				}, {
					kuaizhao: require('../assets/images/idx_13.png'),
					leixing: 'S主播导师',
					zuigao: '127万',
					niandu: '1200万',
					yiwan: '350万',
					daiwan: '850万'
				}, {
					kuaizhao: require('../assets/images/idx_13.png'),
					leixing: 'S主播导师',
					zuigao: '127万',
					niandu: '1200万',
					yiwan: '350万',
					daiwan: '450万'
				}, {
					kuaizhao: require('../assets/images/idx_13.png'),
					leixing: 'S主播导师',
					zuigao: '127万',
					niandu: '1200万',
					yiwan: '350万',
					daiwan: '850万'
				}, {
					kuaizhao: require('../assets/images/idx_13.png'),
					leixing: 'S主播导师',
					zuigao: '127万',
					niandu: '1200万',
					yiwan: '350万',
					daiwan: '850万'
				}, {
					kuaizhao: require('../assets/images/idx_13.png'),
					leixing: 'S主播导师',
					zuigao: '127万',
					niandu: '1200万',
					yiwan: '350万',
					daiwan: '850万'
				}, {
					kuaizhao: require('../assets/images/idx_13.png'),
					leixing: 'S主播导师',
					zuigao: '127万',
					niandu: '1200万',
					yiwan: '350万',
					daiwan: '850万'
				}, {
					kuaizhao: require('../assets/images/idx_13.png'),
					leixing: 'S主播导师',
					zuigao: '127万',
					niandu: '1200万',
					yiwan: '350万',
					daiwan: '850万'
				}, ],
				isFullscreen: false
			}
		},

		components: {
			HeadTime,
			countTo
		},

		mounted() {
			this.init1();
			this.init2();

			window.addEventListener('resize', () => {
				this.chart1.resize();
				this.chart2.resize();

			});

		},

		methods: {
			// 全屏按钮点击
			toggleFullscreen() {
				let full = document.fullscreenElement;
				if (!full) {
					document.documentElement.requestFullscreen();
					this.isFullscreen = true
				} else {
					document.exitFullscreen()
					this.isFullscreen = false
				}
			},


			init1() {
				this.chart1 = Echarts.init(this.$refs.chart1);
				let option = {
					tooltip: {
						trigger: "axis",
						confine: true
					},
					legend: {
						top: 0,
						right: 0,
						icon: 'circle',
						itemWidth: nowSize(8),
						itemHeight: nowSize(8),
						textStyle: {
							color: '#b8c0d8',
							fontSize: nowSize(13)
						}
					},
					grid: {
						left: nowSize(5),
						right: nowSize(15),
						bottom: 0,
						top: nowSize(55),
						containLabel: true
					},
					xAxis: {
						data: ['刘琳', '王云', '丽丽', '王强', '王子轩', '碧桃', '杨超', '代青薛', '王钟', '霓祥云', '王晶', '杨晓岚'],
						axisLabel: {
							show: true,
							textStyle: {
								color: '#8ea5bc',
								fontSize: nowSize(13),
								fontFamily: 'DIN'
							},
							rotate: 30,
							interval: 0
						},
						axisLabel: {
							show: true,
							textStyle: {
								color: '#b8c0d8',
								fontSize: nowSize(14)
							},
							rotate: -60,
							interval: 0
						},
						axisLine: {
							lineStyle: {
								color: 'rgba(71,73,124,.56)' //更改坐标轴颜色
							}
						},
						axisTick: {
							show: false
						},
						splitLine: {
							show: false,
							lineStyle: {
								color: ['rgba(255,255,255,.1)'],
								width: 1,
								type: 'solid'
							}
						}
					},
					yAxis: [{
						type: 'value',
						min: 0,
						//max: 80,
						//interval: 20,
						axisLabel: {
							show: true,
							textStyle: {
								color: '#b8c0d8', //更改坐标轴文字颜色
								fontSize: nowSize(14), //更改坐标轴文字大小
							},
							formatter: '{value}万'
						},
						axisTick: {
							show: false
						},
						axisLine: {
							show: false,
							lineStyle: {
								color: '#fff' //更改坐标轴颜色
							}
						},
						splitLine: {
							show: true,
							lineStyle: {
								color: 'rgba(71,73,124,.56)',
								width: 1,
								type: 'dashed'
							}
						}
					}],
					series: [{
							name: 'GMV',
							type: 'bar',
							itemStyle: {
								normal: {
									color: new Echarts.graphic.LinearGradient(0, 1, 0, 0, [{
										offset: 1,
										color: '#00e4ff',
									}, {
										offset: 0,
										color: '#5039c6',
									}]),
									borderRadius: [10, 10, 0, 0],
								},
							},
							barWidth: nowSize(12),

							data: [15, 20, 45, 35, 45, 30, 55, 50, 40, 35, 25, 30],
						},

					]
				}

				this.chart1.setOption(option);
			},

			init2() {
				this.chart2 = Echarts.init(this.$refs.chart2);
				let option = {
					tooltip: {
						trigger: 'axis',
						confine: true
					},
					toolbox: {
						show: true,
						showTitle: false,
						itemGap: nowSize(18),
						feature: {
							magicType: {
								type: ['bar']
							},
							restore: {},
							saveAsImage: {}
						},
						iconStyle: {
							normal: {
								color: 'transparent',
								borderColor: '#fff',
							},
							emphasis: {
								borderColor: '#ac47b7'
							}
						}
					},
					legend: {
						top: 2,
						right: '29%',
						textStyle: {
							color: '#b8c0d8',
							fontSize: nowSize(13)
						}
					},
					grid: {
						left: nowSize(5),
						right: nowSize(15),
						bottom: 0,
						top: nowSize(55),
						containLabel: true
					},
					xAxis: [{
						type: 'category',
						//boundaryGap: false,
						data: ['刘琳', '王云', '丽丽', '王强', '王子轩', '碧桃', '杨超', '代青薛', '王钟', '霓祥云', '王晶',
							'杨晓岚'
						],
						axisLabel: {
							show: true,
							textStyle: {
								color: '#b8c0d8',
								fontSize: nowSize(14)
							},
							rotate: -60,
							interval: 0
						},
						axisLine: {
							lineStyle: {
								color: 'rgba(71,73,124,.56)' //更改坐标轴颜色
							}
						},
						axisTick: {
							show: false
						},
						splitLine: {
							show: false,
							lineStyle: {
								color: ['rgba(255,255,255,.1)'],
								width: 1,
								type: 'solid'
							}
						}
					}],
					yAxis: [{
						type: 'value',
						min: 0,
						//max: 80,
						//interval: 20,
						axisLabel: {
							show: true,
							textStyle: {
								color: '#b8c0d8', //更改坐标轴文字颜色
								fontSize: nowSize(14), //更改坐标轴文字大小
							},
							formatter: '{value}万'
						},
						axisTick: {
							show: false
						},
						axisLine: {
							show: false,
							lineStyle: {
								color: '#fff' //更改坐标轴颜色
							}
						},
						splitLine: {
							show: true,
							lineStyle: {
								color: 'rgba(71,73,124,.56)',
								width: 1,
								type: 'dashed'
							}
						}
					}],

					series: [{
						name: '人气榜',
						type: 'line',
						data: [15, 20, 45, 35, 45, 30, 55, 50, 40, 35, 25, 30],
						smooth: false,
						symbolSize: 6,
						barWidth: nowSize(12),
						itemStyle: {
							normal: {
								color: "#c35cf2",
								borderWidth: 1,
								lineStyle: {
									color: "#c35cf2"
								},
								areaStyle: {
									color: {
										type: 'linear',
										x: 0,
										y: 0,
										x2: 0,
										y2: 1,
										colorStops: [{
												offset: 0.1,
												color: 'rgba(255,82,229,.5)'
											},
											{
												offset: 1,
												color: 'rgba(255,82,229,0)'
											}
										]
									},
								}
							}
						}
					}]
				};
				this.chart2.setOption(option);
			}








		}

	}
</script>


<style lang="scss" scoped>
	.body {
		height: 100vh;
		overflow: hidden;
		background: url(../assets/images/idx_01.jpg) no-repeat center;
		background-size: cover;

		* {
			color: #fff;
		}

		.head {
			height: 130px;
			background: url(../assets/images/idx_02.png) no-repeat center;
			background-size: cover;
			position: fixed;
			width: 100%;
			left: 0;
			top: 0;
			padding: 0 0 45px;
			display: flex;
			align-items: center;
			justify-content: center;

			.l,
			.r {
				position: absolute;
				top: 0;
				height: 60px;
				display: flex;
				align-items: center;
			}

			.l {
				left: 25px;
			}

			.r {
				right: 25px;
			}

			.logo {
				height: 60px;
				width: auto;
			}

			.fullscreen {
				font-size: 14px;
				width: 75px;
				height: 32px;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #6944f6;
				border-radius: 5px;
				position: absolute;
				right: 25px;
				top: 14px;
				cursor: pointer;

				.ico {
					width: 17px;
					margin-right: 6px;
				}

				&:hover {
					background-color: #5227f8;
				}
			}
		}
	}

	.container {
		margin: 115px 25px 25px;

		.con-l,
		.con-r {
			width: 460px;
		}

		.con-c {
			padding: 0 25px;
		}

		.idx-tt {
			.bt {
				font-size: 20px;
				position: relative;
				line-height: 1;
				padding-bottom: 7px;

				span {
					position: relative;
					z-index: 1;
				}

				&::after {
					content: '';
					width: 145px;
					height: 16px;
					background: url(../assets/images/idx_15.png) no-repeat center;
					background-size: cover;
					position: absolute;
					left: 0;
					bottom: 0;
					z-index: 0;
				}
			}
		}

		.number,
		.number * {
			font-family: 'DIN';
			color: inherit;
			font-size: inherit;
		}

		.m {
			border-radius: 20px;
			background-color: rgba(43, 43, 88, .66);
			margin-bottom: 20px;
			padding: 20px 20px;
			position: relative;

			.detail {
				height: 100%;
				position: relative;
				z-index: 2;
			}

			&:last-child {
				margin-bottom: 0;
			}

			.shuiyin {
				width: 340px;
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				z-index: 0;
			}
		}

		.mod1 {
			height: 135px;
			background: url(../assets/images/idx_06.jpg) no-repeat center;
			background-size: cover;
			display: flex;
			align-items: center;

			.list {
				display: flex;
				justify-content: space-between;
				padding: 0 5%;
				width: 100%;

				.item {
					.fz {
						font-size: 20px;
					}

					.text {
						font-size: 40px;
						line-height: 1;
						margin-top: 9px;
					}
				}
			}
		}

		.mod2,
		.mod6 {
			position: relative;
			padding-bottom: 15px;

			.idx-tt {
				position: absolute;
				left: 0;
				top: 0;
			}
		}

		.mod3 {
			.table {
				margin-top: 25px;

				.thead {
					display: flex;
					align-items: center;

					.th {
						font-size: 14px;
						color: #9698cd;
					}
				}

				.tbody {
					.tr {
						display: flex;
						align-items: center;
						margin: 25px 0 0;

						.imgbox {
							position: relative;
							overflow: initial;
							margin: 0 13px 0 0;

							.hdpic {
								width: 46px;
								height: 46px;
								border-radius: 50%;
							}

							&:before {
								content: '';
								width: 32px;
								height: 32px;
								background-repeat: no-repeat;
								background-position: center;
								background-size: contain;
								position: absolute;
								right: -16px;
								top: -16px;
							}
						}

						.td {
							font-size: 14px;
							line-height: 1.4;
						}

						.smb {
							width: 100px;
							margin-right: 15px;
						}

						.percent {
							color: #f554e7;
						}
					}

					.tr:nth-child(1) .imgbox:before {
						background-image: url(../assets/images/idx_24.png);
					}

					.tr:nth-child(2) .imgbox:before {
						background-image: url(../assets/images/idx_25.png);
					}

					.tr:nth-child(3) .imgbox:before {
						background-image: url(../assets/images/idx_26.png);
					}
				}
			}
		}

		.mod4 {
			height: 380px;
			background: url(../assets/images/idx_17.png) no-repeat center top;
			background-size: cover;
			padding: 20px 35px 0;
			position: relative;

			&:before {
				content: '';
				width: 540px;
				height: 155px;
				background: url(../assets/images/idx_27.png) no-repeat center;
				background-size: contain;
				position: absolute;
				left: 50%;
				top: 25px;
				transform: translate(-46%, 0);
				z-index: 0;
			}

			.top {
				height: 200px;
				position: relative;
				z-index: 2;

				.title {
					font-size: 22px;
					color: #f553e7;
					text-align: center;
				}

				.total {
					text-align: center;
				}

				.total,
				.total * {
					font-size: 80px;
					line-height: 1;
					text-align: center;
					margin-top: 12px;
					font-weight: bold;
					background: linear-gradient(to bottom, #ff52e5 0, #ff52e5 30%, #8965ff);
					background-clip: text;
					color: transparent;
				}

				.bar {
					margin: 15px auto 0;
					display: flex;
					align-items: center;
					justify-content: center;

					.wz {
						font-size: 14px;
						color: #b8c0d8;
					}

					.progress {
						width: 300px;
						margin: 0 10px;
					}



				}
			}

			.list {
				padding: 0 55px 25px;
				display: flex;
				justify-content: space-between;
				margin-top: 20px;
				background: url(../assets/images/idx_18.png) no-repeat center bottom;
				background-size: 100% 24px;

				.item {
					display: flex;
					align-items: flex-start;

					.hdpic {
						width: 56px;
						height: 56px;
						border-radius: 50%;
						margin-right: 15px;
					}

					.name {
						font-size: 16px;
					}

					.num {
						font-size: 22px;
						color: #07d5fa;
						font-family: 'DIN';
						line-height: 1.4;
					}

					.nmz {
						font-size: 14px;
					}
				}
			}
		}

		.mod5 {
			background: url(../assets/images/idx_16.png) no-repeat center;
			background-size: cover;
			padding: 22px 20px 20px;

			.titbox {
				margin-bottom: 10px;
				padding-bottom: 20px;
				text-align: center;
				background: url(../assets/images/idx_12.png) no-repeat center bottom;
				background-size: contain;

				.bt {
					font-size: 22px;
				}
			}


		}

		.mod7 {
			.idx-tt {
				margin-bottom: 10px;
			}

			.total {
				font-size: 40px;
				line-height: 1.2;
				margin-bottom: 22px;
			}

			.list {
				.item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 25px;

					&:last-child {
						margin-bottom: 0;
					}

					.lbox,
					.rbox {
						display: flex;
						align-items: center;
					}

					.imgbox {
						position: relative;
						margin-right: 12px;
						overflow: initial;

						.hdpic {
							width: 46px;
							height: 46px;
							border-radius: 50%;
						}

						.zt {
							font-size: 12px;
							min-width: 44px;
							text-align: center;
							line-height: 16px;
							border-radius: 4px;
							background-color: #6944f6;
							position: absolute;
							left: 50%;
							bottom: -6px;
							transform: translate(-50%, 0);
						}
					}

					.name {
						font-size: 14px;
					}

					.wz {
						font-size: 14px;
						color: #b8c0d8;
					}

					.num {
						font-size: 20px;
						color: #f554e7;
						min-width: 100px;
						text-align: right;
					}
				}
			}

			.shuiyin {
				width: 280px;
				bottom: 20px;
				top: auto;
				transform: translate(-50%, 0);
				opacity: 0.09;
			}
		}

	}
</style>
<style lang="scss">
	.progress {
		height: 16px;
		border-radius: 7px;
		box-shadow: 0 0 10px rgba(180, 94, 245, .56);
		padding: 2px;

		.el-progress {

			.el-progress-bar__outer {
				background: transparent !important;
			}

			.el-progress-bar__inner {
				background: linear-gradient(to right, rgba(255, 82, 229, 0) 0, rgba(255, 82, 229, 0) 30%, rgba(255, 82, 229, 1));
			}
		}
	}


	.mod5 {
		.el-table {
			&::before {
				display: none;
			}

			&,
			.el-table__expanded-cell {
				background: transparent;
			}

			& tr,
			& th.el-table__cell {
				background: transparent;
			}

			thead .cell {
				font-size: 14px;
				color: #9698cd;
				font-weight: normal;
			}

			.cell {
				overflow: initial;
				padding: 5px 0;
			}

			td.el-table__cell,
			th.el-table__cell.is-leaf {
				border-color: #442a71;
			}

			.el-table__body tr:hover>td.el-table__cell {
				background: transparent;
			}

			.ascending .sort-caret.ascending {
				border-bottom-color: #f554e7;
			}

			.descending .sort-caret.descending {
				border-top-color: #f554e7;
			}


			.imgbox {
				position: relative;
				margin: 0 15px 0 10px;

				.pic {
					width: 45px;
					height: 45px;
					border-radius: 50%;
					display: block;
				}

				&:before {
					content: '';
					width: 69px;
					height: 69px;
					background: url(../assets/images/idx_23.png) no-repeat center;
					background-size: cover;
					position: absolute;
					left: -11px;
					top: -13px;
				}
			}

			.el-table__body tr:nth-child(1) .imgbox:before {
				background-image: url(../assets/images/idx_20.png);
				left: -8px;
				top: -14px;
			}

			.el-table__body tr:nth-child(2) .imgbox:before {
				background-image: url(../assets/images/idx_21.png);
				left: -8px;
				top: -14px;
			}

			.el-table__body tr:nth-child(3) .imgbox:before {
				background-image: url(../assets/images/idx_22.png);
				left: -8px;
				top: -14px;
			}


		}




	}
</style>