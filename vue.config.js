// vue.config.js
const path = require("path");
const webpack = require('webpack')

function resolve(dir) {
	return path.join(__dirname, dir);
}
module.exports = {

	css: {
		loaderOptions: {
			// sass: {
			// 	prependData: `@import "@/assets/css/common.scss";` // 对应的scss文件路径
			// },
			// postcss: {
			// 	plugins: [
			// 		require('autoprefixer'),
			// 		require('postcss-plugin-px2rem')({
			// 			rootValue: 192, // 以设计稿 1920 为例，1920 / 10 = 192；换算基数，默认100，此处需要根据设计稿的尺寸来设置，始终设置为设计稿的十分之一，这样页面就会页面展示尺寸
			// 			// unitPrecision: 5, //允许REM单位增长到的十进制数字。
			// 			//propWhiteList: [],  //默认值是一个空数组，这意味着禁用白名单并启用所有属性。
			// 			// propBlackList: [], //黑名单
			// 			exclude: /(node_module)/, //默认false，可以（reg）利用正则表达式排除某些文件夹的方法，例如/(node_module)/ 。如果想把前端UI框架内的px也转换成rem，请把此属性设为默认值
			// 			// selectorBlackList: [], //要忽略并保留为px的选择器
			// 			// ignoreIdentifier: false,  //（boolean/string）忽略单个属性的方法，启用ignoreidentifier后，replace将自动设置为true。
			// 			// replace: true, // （布尔值）替换包含REM的规则，而不是添加回退。
			// 			mediaQuery: false, //（布尔值）允许在媒体查询中转换px。
			// 			minPixelValue: 0 //设置要替换的最小像素值(3px会被转rem)。 默认 0
			// 		}),
			// 	]
			// },
			// sass: {
			// 	// 允许Sass解析器忽略/deep/和::v-deep
			// 	additionalData: `@use "sass:selector";`,
			// 	sassOptions: {
			// 		quietDeps: true, // 忽略特定依赖的警告
			// 	},
			// },
			postcss: {
				postcssOptions: {
					config: true // 启用 postcss.config.js 配置
				}
			}

		}
	},

	pages: { // 全局设置title
		index: {
			// page 的入口
			entry: 'src/main.js',
			// 模板来源
			template: 'public/index.html',
			// 在 dist/index.html 的输出
			filename: 'index.html',
			// 当使用 title 选项时，
			// template 中的 title 标签需要是 <title><%= htmlWebpackPlugin.options.title %></title>
			title: 'name',
			// 在这个页面中包含的块，默认情况下会包含
			// 提取出来的通用 chunk 和 vendor chunk。
			// chunks: ['chunk-vendors', 'chunk-common', 'index']
		}
	},
	publicPath: "./",
	lintOnSave: false,
	chainWebpack: config => {
		//const oneOfsMap = config.module.rule('scss').oneOfs.store
		config.resolve.alias
			.set("@", resolve("src"))
			.set("assets", resolve("src/assets"))
			.set("components", resolve("src/components"))
			.set("views", resolve("src/views"))
			.set("router", resolve("src/router"))


	}




};